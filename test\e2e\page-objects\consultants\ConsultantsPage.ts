import { type Locator, type Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class ConsultantsPage extends BasePage {
  readonly urlPathEngaged: string = '/consultants/engaged';
  readonly urlPathAvailable: string = '/consultants/available';
  readonly consultantsList: Locator;
  readonly consultants: Locator;
  readonly filtersContainer: Locator;
  readonly sharedFilters: {
    search: Locator;
    seniority: Locator;
    company: Locator;
    technologies: Locator;
    minRate: Locator;
    maxRate: Locator;
    residence: Locator;
    profession: Locator;
  };

  readonly engagedFilters: {
    managedBy: Locator;
    engagedFrom: Locator;
    engagedTo: Locator;
    tender: Locator;
  };

  readonly availableFilters: {
    availableFrom: Locator;
    availableTo: Locator;
  };

  constructor(page: Page) {
    super(page);
    this.consultantsList = page.getByRole('list', { name: 'Consultants list' });
    this.consultants = this.consultantsList.getByRole('listitem');
    this.filtersContainer = page.getByRole('list', { name: 'Filters' });
    this.sharedFilters = {
      search: this.filtersContainer.getByRole('listitem', { name: 'Search' }),
      seniority: this.filtersContainer.getByRole('listitem', { name: 'Seniority' }),
      company: this.filtersContainer.getByRole('listitem', { name: 'Company' }),
      technologies: this.filtersContainer.getByRole('listitem', { name: 'Technologies' }),
      minRate: this.filtersContainer.getByRole('listitem', { name: 'Min rate' }),
      maxRate: this.filtersContainer.getByRole('listitem', { name: 'Max rate' }),
      residence: this.filtersContainer.getByRole('listitem', { name: 'Residence' }),
      profession: this.filtersContainer.getByRole('listitem', { name: 'Profession' }),
    };
    this.engagedFilters = {
      managedBy: this.filtersContainer.getByRole('listitem', { name: 'Managed by' }),
      engagedFrom: this.filtersContainer.getByRole('listitem', { name: 'Engaged from' }),
      engagedTo: this.filtersContainer.getByRole('listitem', { name: 'Engaged to' }),
      tender: this.filtersContainer.getByRole('listitem', { name: 'Tender' }),
    };
    this.availableFilters = {
      availableFrom: this.filtersContainer.getByRole('listitem', { name: 'Available from' }),
      availableTo: this.filtersContainer.getByRole('listitem', { name: 'Available to' }),
    };
  }

  async gotoEngaged() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Consultants' }).click();
    await this.page.waitForURL(this.urlPathEngaged);
  }

  async gotoAvailable() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Consultants' }).click();
    await this.page.waitForURL(this.urlPathEngaged);
    await this.subMenu.getByRole('link', { name: 'Available' }).click();
    await this.page.waitForURL(this.urlPathAvailable);
  }
}
