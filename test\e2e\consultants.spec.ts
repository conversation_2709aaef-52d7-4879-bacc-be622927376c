import { expect, test } from '@playwright/test';
import { ConsultantsPage } from './page-objects/consultants/ConsultantsPage';

test('Has consultants engaged/available list and filters', async({ page }) => {
  test.slow();
  const consultantsPage = new ConsultantsPage(page);
  await consultantsPage.gotoEngaged();
  await expect(consultantsPage.consultants.first()).toBeVisible({ timeout: 20000 });
  const filtersThatShouldBeVisible = [...Object.values(consultantsPage.sharedFilters), ...Object.values(consultantsPage.engagedFilters)];
  for (const filter of filtersThatShouldBeVisible) {
    await expect(filter).toBeVisible();
  }
});
