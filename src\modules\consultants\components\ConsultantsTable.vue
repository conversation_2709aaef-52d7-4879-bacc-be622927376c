<script setup lang="ts">
import { reactive } from 'vue';
import ConsultantsTableEntry from './ConsultantsTableEntry.vue';
import { ConsultantTableType, type CandidateRow } from '../types/consultants-types';
import type { NioTableColumn } from '@/common/types/components';
import NioTable from '@/common/components/UI/table/NioTable.vue';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';
import AssignToManagerPopover from '@/modules/tenders/components/tender-candidates/AssignToManagerPopover.vue';
import { useConsultants } from '../composables/useConsultants';
import ConsultantsFilters from './ConsultantsFilters.vue';
import NioTablePagination from '@/common/components/UI/table/NioTablePagination.vue';

const props = defineProps<{
  tableType: ConsultantTableType;
}>();

const { consultants: candidates, filters, page, meta, filterComponents, isLoadingFirstTime, firstLoadHadData } = useConsultants(props.tableType);

const assignToManagerPopoverState = reactive({
  isOpened: false,
  candidate: undefined as CandidateRow<ConsultantTableType.ENGAGED> | undefined,
});

const consultantsTableColumns: NioTableColumn[] = [
  { key: 'name', label: 'Name', width: 1, align: 'left' as const },
  { key: 'profession', label: 'Profession', width: 2, align: 'left' as const },
  { key: 'rate', label: 'Rate', width: 1, align: 'left' as const },
  { key: 'seniority', label: 'Seniority', width: 1, align: 'left' as const },
  ...(props.tableType === ConsultantTableType.ENGAGED ? [{ key: 'managed-by', label: 'Managed by', width: 1, align: 'left' as const }] : []),
  { key: 'availability',
    label: props.tableType === ConsultantTableType.AVAILABLE ? 'Availability' : 'Engaged',
    width: 1,
    align: props.tableType === ConsultantTableType.ENGAGED ? 'left' as const : 'right' as const
  },
  ...(props.tableType === ConsultantTableType.ENGAGED ? [{ key: 'actions', label: 'Actions', width: 1, align: 'right' as const }] : []),
];

const onAssignToManagerPressed = (candidate: CandidateRow<ConsultantTableType.ENGAGED>) => {
  assignToManagerPopoverState.candidate = candidate;
  assignToManagerPopoverState.isOpened = true;
};

const onUpdatedCandidate = (updatedCandidate: CandidateRow<ConsultantTableType.ENGAGED>) => {
  const idx = candidates.value.findIndex(v => v.id === updatedCandidate.id);
  if (idx !== -1) {
    candidates.value[idx] = updatedCandidate;
  }
};

const skeletonClass = 'w-full h-[22px] bg-nio-grey-100 rounded-10 mb-1';
</script>

<template>
  <div class="w-full">
    <ConsultantsFilters
      v-show="firstLoadHadData"
      v-model:filters="filters"
      class="mb-3"
      :filter-components="filterComponents"
    />
    <NioTable v-if="isLoadingFirstTime" :columns="consultantsTableColumns">
      <NioTableRow v-for="i in 5" :key="i" :columns="consultantsTableColumns">
        <template #name>
          <div :class="skeletonClass" />
        </template>
        <template #profession>
          <div :class="skeletonClass" />
        </template>
        <template #rate>
          <div :class="skeletonClass" />
        </template>
        <template #seniority>
          <div :class="skeletonClass" />
        </template>
        <template #availability>
          <div :class="skeletonClass" />
        </template>
        <template v-if="tableType === ConsultantTableType.ENGAGED" #managed-by>
          <div :class="skeletonClass" />
        </template>
        <template v-if="tableType === ConsultantTableType.ENGAGED" #actions>
          <div :class="skeletonClass" />
        </template>
      </NioTableRow>
    </NioTable>

    <template v-else>
      <NioTable
        v-show="candidates?.length"
        :columns="consultantsTableColumns"
        role="list"
        aria-label="Consultants list"
      >
        <ConsultantsTableEntry
          v-for="(candidate, index) in candidates"
          :key="candidate.id"
          :candidate-entry-data="candidate"
          :index="index"
          :table-type="tableType"
          :columns="consultantsTableColumns"
          role="listitem"
          @assign-to-manager="onAssignToManagerPressed(candidate as CandidateRow<ConsultantTableType.ENGAGED>)"
        />
      </NioTable>
      <div v-if="!candidates?.length" class="text-lg mb-4 text-center">
        No candidates found
      </div>
      <NioTablePagination
        v-if="meta"
        v-model:page="page"
        class="mt-5"
        :meta="meta"
      />
    </template>
    <Suspense>
      <AssignToManagerPopover
        v-model="assignToManagerPopoverState.isOpened"
        :candidate="assignToManagerPopoverState.candidate"
        @updated-candidate="onUpdatedCandidate"
      />
    </Suspense>
  </div>
</template>
